use std::collections::HashMap;
use once_cell::sync::Lazy;

#[derive(Debug)]
pub struct ClassificationInfo {
    pub classtype: &'static str,
    pub priority: &'static str,
    pub index: u8,
}

pub static CLASSIFICATIONS_DICT: Lazy<HashMap<&'static str, ClassificationInfo>> = Lazy::new(|| {
    let mut m = HashMap::new();
    m.insert("Attempted Administrator Privilege Gain", ClassificationInfo { classtype: "attempted-admin", priority: "high", index: 1 });
    m.insert("Attempted User Privilege Gain", ClassificationInfo { classtype: "attempted-user", priority: "high", index: 1 });
    m.insert("Inappropriate Content was Detected", ClassificationInfo { classtype: "inappropriate-content", priority: "high", index: 1 });
    m.insert("Potential Corporate Privacy Violation", ClassificationInfo { classtype: "policy-violation", priority: "high", index: 1 });
    m.insert("Executable code was detected", ClassificationInfo { classtype: "shellcode-detect", priority: "high", index: 1 });
    m.insert("Successful Administrator Privilege Gain", ClassificationInfo { classtype: "successful-admin", priority: "high", index: 1 });
    m.insert("Successful User Privilege Gain", ClassificationInfo { classtype: "successful-user", priority: "high", index: 1 });
    m.insert("A Network Trojan was detected", ClassificationInfo { classtype: "trojan-activity", priority: "high", index: 1 });
    m.insert("Unsuccessful User Privilege Gain", ClassificationInfo { classtype: "unsuccessful-user", priority: "high", index: 1 });
    m.insert("Web Application Attack", ClassificationInfo { classtype: "web-application-attack", priority: "high", index: 1 });
    m.insert("Attempted Denial of Service", ClassificationInfo { classtype: "attempted-dos", priority: "medium", index: 2 });
    m.insert("Attempted Information Leak", ClassificationInfo { classtype: "attempted-recon", priority: "medium", index: 2 });
    m.insert("Potentially Bad Traffic", ClassificationInfo { classtype: "bad-unknown", priority: "medium", index: 2 });
    m.insert("Attempt to login by a default username and password", ClassificationInfo { classtype: "default-login-attempt", priority: "medium", index: 2 });
    m.insert("Detection of a Denial of Service Attack", ClassificationInfo { classtype: "denial-of-service", priority: "medium", index: 2 });
    m.insert("Misc Attack", ClassificationInfo { classtype: "misc-attack", priority: "medium", index: 2 });
    m.insert("Detection of a non-standard protocol or event", ClassificationInfo { classtype: "non-standard-protocol", priority: "medium", index: 2 });
    m.insert("Decode of an RPC Query", ClassificationInfo { classtype: "rpc-portmap-decode", priority: "medium", index: 2 });
    m.insert("Denial of Service", ClassificationInfo { classtype: "successful-dos", priority: "medium", index: 2 });
    m.insert("Large Scale Information Leak", ClassificationInfo { classtype: "successful-recon-largescale", priority: "medium", index: 2 });
    m.insert("Information Leak", ClassificationInfo { classtype: "successful-recon-limited", priority: "medium", index: 2 });
    m.insert("A suspicious filename was detected", ClassificationInfo { classtype: "suspicious-filename-detect", priority: "medium", index: 2 });
    m.insert("An attempted login using a suspicious username was detected", ClassificationInfo { classtype: "suspicious-login", priority: "medium", index: 2 });
    m.insert("A system call was detected", ClassificationInfo { classtype: "system-call-detect", priority: "medium", index: 2 });
    m.insert("A client was using an unusual port", ClassificationInfo { classtype: "unusual-client-port-connection", priority: "medium", index: 2 });
    m.insert("Access to a potentially vulnerable web application", ClassificationInfo { classtype: "web-application-activity", priority: "medium", index: 2 });
    m.insert("Generic ICMP event", ClassificationInfo { classtype: "icmp-event", priority: "low", index: 3 });
    m.insert("Misc activity", ClassificationInfo { classtype: "misc-activity", priority: "low", index: 3 });
    m.insert("Detection of a Network Scan", ClassificationInfo { classtype: "network-scan", priority: "low", index: 3 });
    m.insert("Not Suspicious Traffic", ClassificationInfo { classtype: "not-suspicious", priority: "low", index: 3 });
    m.insert("Generic Protocol Command Decode", ClassificationInfo { classtype: "protocol-command-decode", priority: "low", index: 3 });
    m.insert("A suspicious string was detected", ClassificationInfo { classtype: "string-detect", priority: "low", index: 3 });
    m.insert("Unknown Traffic", ClassificationInfo { classtype: "unknown", priority: "low", index: 3 });
    m.insert("A TCP connection was detected", ClassificationInfo { classtype: "tcp-connection", priority: "very low", index: 4 });
    m
});

pub static CLASSIFICATIONS__CRITICALS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    CLASSIFICATIONS_DICT.iter()
        .filter(|(_, v)| v.index == 1)
        .map(|(k, _)| *k)
        .collect()
});

pub static CLASSIFICATIONS__WARNINGS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    CLASSIFICATIONS_DICT.iter()
        .filter(|(_, v)| v.index == 2)
        .map(|(k, _)| *k)
        .collect()
});

pub static CLASSIFICATIONS__LOWS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    CLASSIFICATIONS_DICT.iter()
        .filter(|(_, v)| v.index == 3)
        .map(|(k, _)| *k)
        .collect()
});

pub static CLASSIFICATIONS__VERY_LOWS: Lazy<Vec<&'static str>> = Lazy::new(|| {
    CLASSIFICATIONS_DICT.iter()
        .filter(|(_, v)| v.index == 4)
        .map(|(k, _)| *k)
        .collect()
});
