use std::net::{
    Ip<PERSON>ddr,
    Ipv4Addr,
    Ipv6Addr,
};

pub fn is_cidr(string: &str) -> bool {
    // __HAS_TEST__

    // Check if string contains a slash followed by digits
    if !string.contains('/') {
        return false;
    }

    let parts: Vec<&str> = string.split('/').collect();
    if parts.len() != 2 {
        return false;
    }

    let ip_part = parts[0];
    let prefix_part = parts[1];

    // Validate prefix part is a valid number
    let prefix = match prefix_part.parse::<u8>() {
        Ok(p) => p,
        Err(_) => return false,
    };

    // Check if IP part is valid IPv4 or IPv6
    if is_ip_v4(ip_part) {
        // IPv4 prefix must be between 0 and 32
        prefix <= 32
    } else if is_ip_v6(ip_part) {
        // IPv6 prefix must be between 0 and 128
        prefix <= 128
    } else {
        false
    }
}

pub fn is_ip(string: &str) -> bool {
    // __HAS_TEST__

    is_ip_v4(string) || is_ip_v6(string)
}

pub fn is_ip_v4(string: &str) -> bool {
    // __HAS_TEST__

    // Use std::net::Ipv4Addr for validation instead of manual parsing - much faster
    string.parse::<Ipv4Addr>().is_ok()
}

pub fn is_ip_v6(string: &str) -> bool {
    // __HAS_TEST__

    // Use std::net::Ipv6Addr for validation instead of regex - much faster
    string.parse::<Ipv6Addr>().is_ok()
}

pub fn is_private(string: &str) -> bool {
    // __HAS_TEST__

    is_private_v4(string) || is_private_v6(string)
}

pub fn is_private_v4(string: &str) -> bool {
    // __HAS_TEST__

    // Use std::net::Ipv4Addr's built-in is_private method - much faster and more accurate
    if let Ok(ip) = string.parse::<Ipv4Addr>() {
        ip.is_private()
    } else {
        false
    }
}

pub fn is_private_v6(string: &str) -> bool {
    // __HAS_TEST__

    // Use std::net::Ipv6Addr for validation and checking private ranges
    if let Ok(ip) = string.parse::<Ipv6Addr>() {
        // Check for loopback (::1)
        if ip.is_loopback() {
            return true;
        }

        // Check for Unique Local Addresses (ULA) - fc00::/7
        let segments = ip.segments();
        if segments[0] & 0xfe00 == 0xfc00 {
            return true;
        }

        // Check for Link-Local Addresses - fe80::/10
        if segments[0] & 0xffc0 == 0xfe80 {
            return true;
        }

        false
    } else {
        false
    }
}

pub fn get_hosts(ip: &str) -> Vec<String> {
    // __HAS_TEST__

    // Parse the CIDR notation
    if !ip.contains('/') {
        return vec![];
    }

    let parts: Vec<&str> = ip.split('/').collect();
    if parts.len() != 2 {
        return vec![];
    }

    let ip_part = parts[0];
    let prefix_len = match parts[1].parse::<u8>() {
        Ok(p) => p,
        Err(_) => return vec![],
    };

    // Parse the IP address
    let addr = match ip_part.parse::<IpAddr>() {
        Ok(addr) => addr,
        Err(_) => return vec![],
    };

    match addr {
        IpAddr::V4(ipv4) => _get_hosts_ipv4(ipv4, prefix_len),
        IpAddr::V6(ipv6) => _get_hosts_ipv6(ipv6, prefix_len),
    }
}

fn _get_hosts_ipv4(network: Ipv4Addr, prefix_len: u8) -> Vec<String> {
    if prefix_len > 32 {
        return vec![];
    }

    if prefix_len == 32 {
        return vec![network.to_string()];
    }

    let network_u32 = u32::from(network);
    let host_bits = 32 - prefix_len;

    // Handle edge cases to prevent overflow
    if host_bits >= 31 {
        return vec![];
    }

    let total_addresses = 1u32 << host_bits;
    if total_addresses <= 2 {
        return vec![];
    }

    let num_hosts = total_addresses - 2;

    if num_hosts > 65536 {
        return vec![];
    }

    let network_mask = !((1u32 << host_bits) - 1);
    let network_addr = network_u32 & network_mask;

    let mut hosts = Vec::new();
    for i in 1..=num_hosts {
        let host_addr = network_addr + i;
        hosts.push(Ipv4Addr::from(host_addr).to_string());
    }

    hosts
}

fn _get_hosts_ipv6(network: Ipv6Addr, prefix_len: u8) -> Vec<String> {
    if prefix_len > 128 {
        return vec![];
    }

    if prefix_len == 128 {
        return vec![network.to_string()];
    }

    let host_bits = 128 - prefix_len;
    if host_bits > 16 {
        return vec![];
    }

    let network_u128 = u128::from(network);

    // Handle edge cases to prevent overflow
    if host_bits >= 63 {
        return vec![];
    }

    let total_addresses = 1u128 << host_bits;
    if total_addresses <= 1 {
        return vec![];
    }

    // IPv6 doesn't have broadcast addresses like IPv4, so we only subtract 1 for network address
    let num_hosts = total_addresses - 1;

    if num_hosts > 65536 {
        return vec![];
    }

    let network_mask = !((1u128 << host_bits) - 1);
    let network_addr = network_u128 & network_mask;

    let mut hosts = Vec::new();
    for i in 1..=num_hosts {
        let host_addr = network_addr + i;
        hosts.push(Ipv6Addr::from(host_addr).to_string());
    }

    hosts
}
