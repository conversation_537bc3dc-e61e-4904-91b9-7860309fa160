use std::collections::HashMap;

use aho_corasick::AhoCorasick;
use once_cell::sync::Lazy;
use regex::Regex;

use crate::utils::{
    normalize_date,
    normalize_dns_question_name,
    normalize_time,
};

use crate::utils_classes::{
    DaemonConfig,
    DHCPConfig,
    DNSConfig,
    FilterLogConfig,
    // MYSQLValue,
    RouterConfig,
    RouterBoardConfig,
    SnortConfig,
    SquidConfig,
    SwitchConfig,
    UserAuditConfig,
    UserNoticeConfig,
    UserWarningConfig,
    VMwareConfig,
    VPNServerConfig,
    WindowsServerConfig,
};

use crate::utils_patterns::{
    DAEMON_PATTERN,
    DHCP_PATTERN,
    DHCP_REST_PATTERN,
    DNS_PATTERN,
    DNS_REST_PATTERN,
    FILTERLOG_PATTERN,
    ROUTER_PATTERN,
    RO<PERSON>ERBOARD_PATTERN,
    SNORT_PATTERN,
    SQUID_PATTERN,
    SWITCH_PATTERN,
    USERAUDIT_PATTERN,
    USERNOTICE_PATTERN,
    USERWARNING_PATTERN,
    VMWARE_PATTERN,
    VPNSERVER_PATTERN,
    WINDOWSSERVER_PATTERN,
    WS_AN_AD_PATTERN,
    WS_SW_PATTERN,
};

#[derive(Debug, Clone, Copy, PartialEq, Eq, Hash)]
pub enum ConfigType {
    Daemon,
    DHCP,
    DNS,
    FilterLog,
    Router,
    RouterBoard,
    Snort,
    Squid,
    Switch,
    UserAudit,
    UserNotice,
    UserWarning,
    VMware,
    VPNServer,
    WindowsServer,
}

impl ConfigType {
    #[inline(always)]
    pub fn get_slug(&self) -> &'static str {
        match self {
            ConfigType::Daemon        => "daemon",
            ConfigType::DHCP          => "dhcp",
            ConfigType::DNS           => "dns",
            ConfigType::FilterLog     => "filterlog",
            ConfigType::Router        => "router",
            ConfigType::RouterBoard   => "routerboard",
            ConfigType::Snort         => "snort",
            ConfigType::Squid         => "squid",
            ConfigType::Switch        => "switch",
            ConfigType::UserAudit     => "useraudit",
            ConfigType::UserNotice    => "usernotice",
            ConfigType::UserWarning   => "userwarning",
            ConfigType::VMware        => "vmware",
            ConfigType::VPNServer     => "vpnserver",
            ConfigType::WindowsServer => "windowsserver",
        }
    }
}

static INVALID_LINES_MATCHER: Lazy<AhoCorasick> = Lazy::new(|| {
    AhoCorasick::new([
        "ERROR name exceeds safe print buffer length",
        "ERROR length byte",
        "leads outside message",
        "Exiting on signal",
        "Now monitoring attacks",
        "spp_arpspoof",
        "because it is a directory, not a file"
    ]).unwrap()
});

#[inline(always)]
pub fn _is_invalid_ln(ln: &str) -> bool {
    // __HAS_TEST__
    ln.is_empty()
        || INVALID_LINES_MATCHER.is_match(ln)
        || !ln.chars().next().map_or(false, |c| c.is_ascii_digit())
}

#[inline(always)]
fn _invalid_line_sections(
    object_list_of_names_and_addresses: &[String],
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
    line_object: &str,
    line_event_type: &str,
    line_alert_type: &str,
) -> bool {
    // early return if none of them exists
    if event_types.is_empty() && filterby.is_empty() && line_object.is_empty() {
        return false;
    }

    // added 'if line_object' because
    // DHCP, DNS and VPN Server have no line_object
    if !line_object.is_empty()
        && !object_list_of_names_and_addresses.iter().any(|s| s == line_object)
    {
        return true;
    }

    if !event_types.is_empty()
        && !event_types.iter().any(|s| s == line_event_type)
    {
        return true;
    }

    if filterby_is_in_alert_type && !line_alert_type.contains(filterby) {
        return true;
    }

    false
}

#[inline(always)]
fn _extract_matches_from_pattern<'a, const N: usize>(
    string: &'a str,
    pattern: &Regex,
) -> Option<[&'a str; N]> {
    let caps = pattern.captures(string)?;

    // use array initialization with iterator for better performance
    let mut arr = [""; N];
    let mut i = 0;
    while i < N {
        arr[i] = caps.get(i + 1).map_or("", |m| m.as_str());
        i += 1;
    }
    Some(arr)
}

#[inline(always)]
fn _map_object_ip_to_object_name<'a>(
    line_object: &'a str,
    object_dict_of_addresses_and_names: &'a HashMap<String, String>,
) -> &'a str {
    /// Maps an IP address or identifier to its corresponding object name, if available.
    ///
    /// This function looks up a `line_object` (typically an IP address or object name)
    /// in the provided `object_dict_of_addresses_and_names` and returns the mapped
    /// object name if found. If there is no mapping, it returns the original input.
    ///
    /// # Parameters
    /// - `line_object`: The IP address or identifier to map.
    /// - `object_dict_of_addresses_and_names`: A reference to a HashMap that maps IPs or identifiers to object names.
    ///
    /// # Returns
    /// - The mapped object name if found in the dictionary, otherwise the original `line_object`.
    ///
    /// # Example
    /// ```
    /// let map = HashMap::from([("***********".to_string(), "Object-1".to_string())]);
    /// assert_eq!(
    ///     _map_object_ip_to_object_name("***********", &map),
    ///     "Object-1"
    /// );
    /// assert_eq!(
    ///     _map_object_ip_to_object_name("Object-1", &map),
    ///     "Object-1"
    /// );
    /// ```

    match object_dict_of_addresses_and_names.get(line_object) {
        Some(mapped) => mapped,
        None => line_object,
    }
}

#[inline(always)]
fn _parse_daemon(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &DAEMON_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_dhcp(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if ln.contains("ID,Date,Time,Description,IP Address,Host Name,MAC Address,User Name") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern::<6>(ln, &DHCP_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        rest,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // pass empty string instead of line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let rest_splited = match _extract_matches_from_pattern::<19>(rest, &DHCP_REST_PATTERN) {
        Some(r_s) => r_s,
        None => return (None, None),
    };

    let (
        event_id,
        date,  // 05/12/23
        time,
        description, source_ip, host_name, mac_address, user_name,
        transaction_id, qresult, probation_time, correlation_id, dhc_id,
        vendor_class_hex, vendor_class_ascii, user_class_hex,
        user_class_ascii, relay_agent_information, dns_reg_error,
    ) = (
        rest_splited[0], rest_splited[1], rest_splited[2], rest_splited[3],
        rest_splited[4], rest_splited[5], rest_splited[6], rest_splited[7],
        rest_splited[8], rest_splited[9], rest_splited[10], rest_splited[11],
        rest_splited[12], rest_splited[13], rest_splited[14], rest_splited[15],
        rest_splited[16], rest_splited[17], rest_splited[18],
    );

    let row = vec![
        normalize_date(date),  // 05/12/23 -> 2023-05-12
        time.to_string(),
        event_id.to_string(),
        description.to_string(),
        source_ip.to_string(),
        host_name.to_string(),
        mac_address.to_string(),
        user_name.to_string(),
        transaction_id.to_string(),
        qresult.to_string(),
        probation_time.to_string(),
        correlation_id.to_string(),
        dhc_id.to_string(),
        vendor_class_hex.to_string(),
        vendor_class_ascii.to_string(),
        user_class_hex.to_string(),
        user_class_ascii.to_string(),
        relay_agent_information.to_string(),
        dns_reg_error.to_string(),
    ];

    (None, Some(row))
}

#[inline(always)]
fn _parse_dns(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &DNS_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        rest,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // pass empty string instead of line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    // rest may be:
    //
    // 5/26/2023 8:40:00 PM 1510 PACKET 00000162B0 UDP Rcv *********** 0007   Q [0000      NOERROR]  A    (3)www(62)gooooooogle(3)com(0)
    // 5/26/2023 8:40:00 PM 1510 PACKET 00000162B3 UDP Rcv *********** 0005   Q [0000      NOERROR]  PTR  (3)216(2)58(3)202(1)4(7)in-addr(4)arpa(0)
    // 5/26/2023 12:13:45 PM 1018 PACKET 00000162B UDP Snd *******     6153   Q [0001   D  NOERROR]  AAAA (2)ds(9)kaspersky(3)com(0)
    // 5/26/2023 12:13:47 PM 1904 PACKET 00000162B UDP Rcv *********** c312   Q [0001   D  NOERROR]  A    (3)dc1(3)ksn(14)kaspersky-labs(3)com(0)
    // 5/26/2023 12:13:42 PM 1018 PACKET 00000162B UDP Snd *********** 8916 R Q [8281   DR SERVFAIL] AAAA (17)connectivitycheck(7)gstatic(3)com(0)
    // 5/26/2023 12:13:42 PM 1018 PACKET 00000162B UDP Snd *********** bb63 R Q [8281   DR SERVFAIL] A    (17)connectivitycheck(7)gstatic(3)com(0)
    // 5/26/2023 12:13:28 PM 1B00 PACKET 00000162B UDP Snd *********** cccf R Q [8385 A DR NXDOMAIN] A    (5)graph(8)facebook(3)com(3)abc(5)local(0)
    // 5/26/2023 12:13:41 PM 1510 PACKET 00000162A UDP Snd *********** 275c R Q [8385 A DR NXDOMAIN] AAAA (1)0(7)example(4)pool(3)ntp(3)org(3)abc(5)local(0)

    let rest_splited = match _extract_matches_from_pattern::<17>(rest, &DNS_REST_PATTERN) {
        Some(r_s) => r_s,
        None => return (None, None),
    };

    let (
        date,  // 05/12/23
        time,  // 8:17:46
        am_pm,  // PM
        thread_id, context, internal_packet_identifier, udp_tcp_indicator,
        send_receive_indicator, source_ip, xid_hex, query_response,
        opcode, flags_hex, flags_char_codes, responsecode, question_type,
        question_name,
    ) = (
        rest_splited[0], rest_splited[1], rest_splited[2], rest_splited[3],
        rest_splited[4], rest_splited[5], rest_splited[6], rest_splited[7],
        rest_splited[8], rest_splited[9], rest_splited[10], rest_splited[11],
        rest_splited[12], rest_splited[13], rest_splited[14], rest_splited[15],
        rest_splited[16],
    );

    let row = vec![
        normalize_date(date),  // 05/12/23 -> 2023-05-12
        normalize_time(&format!("{} {}", time, am_pm)),  // 8:17:46 PM -> 20:17:46
        thread_id.to_string(),
        context.to_string(),
        internal_packet_identifier.to_string(),
        udp_tcp_indicator.to_string(),
        send_receive_indicator.to_string(),
        source_ip.to_string(),
        xid_hex.to_string(),
        query_response.to_string(),
        opcode.to_string(),
        flags_hex.to_string(),
        flags_char_codes.to_string(),
        responsecode.to_string(),
        question_type.to_string(),
        normalize_dns_question_name(question_name).to_lowercase(),
    ];

    (None, Some(row))
}

#[inline(always)]
fn _parse_filterlog(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &FILTERLOG_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        rest,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let rest_splited_: Vec<&str> = rest.split(',').collect();
    // __ITER__
    let mut rest_splited = rest_splited_.iter();

    // rule_number = String::new();
    // sub_rule_number = String::new();
    // anchor = String::new();
    let mut tracking_id = String::new();
    let mut real_interface = String::new();
    let mut reason = String::new();
    let mut action = String::new();
    let mut direction = String::new();
    let mut ip_version = String::new();
    // tos = String::new();
    // ecn = String::new();
    // ttl = String::new();
    // id_ = String::new();
    // offset = String::new();
    // flags = String::new();
    // class_ = String::new();
    // flow_label = String::new();
    // hop_limit = String::new();
    // protocol_id = String::new();
    let mut protocol_name = String::new();
    // length = String::new();
    let mut source_ip = String::new();
    let mut destination_ip = String::new();
    let mut source_port = String::new();
    let mut destination_port = String::new();
    // data_length = String::new();
    // tcp_flags = String::new();
    // sequence_number = String::new();
    // ack = String::new();
    // window = String::new();
    // urg = String::new();
    // options = String::new();
    // type_ = String::new();
    // ttl_or_hop_limit = String::new();
    // vhid = String::new();
    // version = String::new();
    // advskew = String::new();
    // advbase = String::new();

    if let (Some(_), Some(_), Some(_), Some(&tracking_id_str), Some(&real_interface_str),
            Some(&reason_str), Some(&action_str), Some(&direction_str), Some(&ip_version_str)) =
        (rest_splited.next(), rest_splited.next(), rest_splited.next(), rest_splited.next(), rest_splited.next(),
         rest_splited.next(), rest_splited.next(), rest_splited.next(), rest_splited.next()) {

        tracking_id    = tracking_id_str.to_string();
        real_interface = real_interface_str.to_string();
        reason         = reason_str.to_string();
        action         = action_str.to_string();
        direction      = direction_str.to_string();
        ip_version     = ip_version_str.to_string();

        if ip_version_str == "4" {
            rest_splited.next();  // tos
            rest_splited.next();  // ecn
            rest_splited.next();  // ttl
            rest_splited.next();  // id_
            rest_splited.next();  // offset
            rest_splited.next();  // flags
            rest_splited.next();  // protocol_id

            if let Some(&protocol_name_str) = rest_splited.next() {
                protocol_name = protocol_name_str.to_lowercase();
            } else {
                return (None, None);
            }
        } else if ip_version_str == "6" {
            rest_splited.next();  // class_
            rest_splited.next();  // flow_label
            rest_splited.next();  // hop_limit

            if let (Some(&protocol_id_str), Some(&protocol_name_str)) = (rest_splited.next(), rest_splited.next()) {
                protocol_name = protocol_id_str.to_lowercase();
            } else {
                return (None, None);
            }
        } else {
            return (None, None);
        }

        rest_splited.next();  // length

        // Get source and destination IPs
        if let (Some(&source_ip_str), Some(&destination_ip_str)) = (rest_splited.next(), rest_splited.next()) {
            source_ip = source_ip_str.to_string();
            destination_ip = destination_ip_str.to_string();
        } else {
            return (None, None);
        }

        if protocol_name == "udp" {
            if let (Some(&source_port_str), Some(&destination_port_str)) = (rest_splited.next(), rest_splited.next()) {
                source_port = source_port_str.to_string();
                destination_port = destination_port_str.to_string();
                rest_splited.next();  // data_length
            }
        } else if protocol_name == "tcp" {
            if let (Some(&source_port_str), Some(&destination_port_str)) = (rest_splited.next(), rest_splited.next()) {
                source_port = source_port_str.to_string();
                destination_port = destination_port_str.to_string();
                rest_splited.next();  // data_length
                rest_splited.next();  // tcp_flags
                rest_splited.next();  // sequence_number
                rest_splited.next();  // ack
                rest_splited.next();  // window
                rest_splited.next();  // urg
                rest_splited.next();  // options
            }
        }
        // else if protocol_name == "tcp" {
        //     rest_splited.next();  // type_
        //     rest_splited.next();  // ttl_or_hop_limit
        //     rest_splited.next();  // vhid
        //     rest_splited.next();  // version
        //     rest_splited.next();  // advskew
        //     rest_splited.next();  // advbase
        // }
    } else {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),

        // moved up from JUMP_9
        // protocol_id,
        protocol_name,
        // length,
        source_ip,
        destination_ip,
        source_port,
        destination_port,

        // rule_number,
        // sub_rule_number,
        // anchor,
        tracking_id,
        real_interface,
        reason,
        action,
        direction,
        // ip_version,
        // tos,
        // ecn,
        // ttl,
        // id_,
        // offset,
        // flags,
        // class_,
        // flow_label,
        // hop_limit,

        // JUMP_9

        // data_length,
        // tcp_flags,
        // sequence_number,
        // ack,
        // window,
        // urg,
        // options,
        // type_,
        // ttl_or_hop_limit,
        // vhid,
        // version,
        // advskew,
        // advbase,
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_router(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &ROUTER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_routerboard(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &ROUTERBOARD_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_snort(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<14>(ln, &SNORT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        gidsid, description, classification, priority,
        protocol, source_ip, source_port, destination_ip, destination_port,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
        splited[6], splited[7], splited[8], splited[9], splited[10], splited[11],
        splited[12], splited[13],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        gidsid.to_string(),
        description.to_string(),
        classification.to_string(),
        priority.to_string(),
        protocol.to_string(),
        source_ip.to_string(),
        source_port.to_string(),
        destination_ip.to_string(),
        destination_port.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_squid(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<17>(ln, &SQUID_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        timestamp, duration, source_ip, request_status, status_code, transfer,
        http_method, url, client_identity, peer_code, destination_ip, content_type,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
        splited[6], splited[7], splited[8], splited[9], splited[10], splited[11],
        splited[12], splited[13], splited[14], splited[15], splited[16],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        // timestamp.to_string(),
        duration.to_string(),
        source_ip.to_string(),
        request_status.to_string(),
        status_code.to_string(),
        transfer.to_string(),
        http_method.to_string(),
        url.to_string().to_lowercase(),
        client_identity.to_string(),
        peer_code.to_string(),
        destination_ip.to_string(),
        content_type.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_switch(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &SWITCH_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_useraudit(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if !ln.contains("Successful login") && !ln.contains("User logged out") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern::<6>(ln, &USERAUDIT_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        // line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_usernotice(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<10>(ln, &USERNOTICE_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        server, user, destination_ip, port, status,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
        splited[6], splited[7], splited[8], splited[9],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        // line_event_type.to_string(),
        // line_alert_type.to_string(),
        server.to_string(),
        user.to_string(),
        destination_ip.to_string(),
        port.to_string(),
        status.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_userwarning(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &USERWARNING_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        // line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_vmware(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<6>(ln, &VMWARE_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
    ];

    (Some(line_object), Some(row))
}

#[inline(always)]
fn _parse_vpnserver(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    let splited = match _extract_matches_from_pattern::<11>(ln, &VPNSERVER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        domain,           // MYDOMAIN
        username,         // n.peterson
        port,             // VPN1-123
        active_for,       // 172 minutes 12 seconds
        sent,             // 39146847 (sent)
        received,         // 7613881 (received)
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5],
        splited[6], splited[7], splited[8], splited[9], splited[10],
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        "",  // pass empty string instead of line_object
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }

    let row = vec![
        date.to_string(),
        time.to_string(),
        domain.to_string(),
        username.to_string(),
        port.to_string(),
        active_for.to_string(),
        sent.to_string(),
        received.to_string(),
    ];

    (None, Some(row))
}

#[inline(always)]
fn _parse_windowsserver(
    ln: &str,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
    event_types: &[String],
    filterby_is_in_alert_type: bool,
    filterby: &str,
) -> (Option<String>, Option<Vec<String>>) {
    if ln.contains("[dns]") || ln.contains("[dhcp]") {
        return (None, None);
    }

    let splited = match _extract_matches_from_pattern::<6>(ln, &WINDOWSSERVER_PATTERN) {
        Some(s) => s,
        None => return (None, None),
    };

    let (
        date,
        time,
        line_object,      // Sensor-1 OR ***********
        line_event_type,  // (daemon/err), ...
        line_alert_type,  // [snort]
        message,
    ) = (
        splited[0], splited[1], splited[2], splited[3], splited[4], splited[5].replace("\t", " "),
    );

    if _invalid_line_sections(
        object_list_of_names_and_addresses,

        event_types,
        filterby_is_in_alert_type,
        filterby,

        line_object,
        line_event_type,
        line_alert_type,
    ) {
        return (None, None);
    }



    // START __prepare_more_variables__

    // event_id
    let message_splited: Vec<&str> = message.split_whitespace().collect();
    let event_id = if message_splited.len() > 4 {
        message_splited[4].to_string()  // '4625', 'N/A', ...
    } else {
        "".to_string()
    };

    // category
    let category = WindowsServerConfig::get_category_from_event_id(&event_id);

    // potential_criticality
    let potential_criticality = WindowsServerConfig::EVENT_IDS_AND_POTENTIAL_CRITICALITIES.value().value_dict()
                                .get(&event_id)
                                .cloned()
                                .unwrap_or_else(|| "".to_string());  // High, Medium, ...

    // account_name, account_domain
    let account_matches = match _extract_matches_from_pattern::<2>(&message, &WS_AN_AD_PATTERN) {
        Some(a_m) => {
            (a_m[0].to_string(), a_m[1].to_string())  // ('SYSTEM', 'NT AUTHORITY')
        },
        None => {
            ("".to_string(), "".to_string())
        }
    };
    //
    let (account_name, account_domain) = account_matches;

    // source workstation
    let sw_matches = match _extract_matches_from_pattern::<1>(&message, &WS_SW_PATTERN) {
        Some(sw_m) => {
            sw_m[0].to_string()  // Windows7
        },
        None => {
            "".to_string()
        }
    };
    //
    let source_workstation = sw_matches;

    // END __prepare_more_variables__


    let line_object = _map_object_ip_to_object_name(
        line_object,
        object_dict_of_addresses_and_names
    ).to_string();

    let row = vec![
        date.to_string(),
        time.to_string(),
        // line_event_type.to_string(),
        line_alert_type.to_string(),
        message.to_string(),
        event_id,
        category,
        potential_criticality,
        account_name,
        account_domain,
        source_workstation,
    ];

    (Some(line_object), Some(row))
}

static _PRECOMPUTED_VALUES: Lazy<HashMap<&'static str, (String, String, bool, Vec<String>)>> = Lazy::new(|| {
    let mut map = HashMap::new();

    macro_rules! add_config {
        ($typ:ident, $slug:expr) => {{
            let slug = $typ::SLUG.value().value_string();
            let filterby = $typ::FILTERBY.value().value_string();
            let filterby_is_in_alert_type = $typ::FILTERBY_IS_IN_ALERT_TYPE.value().value_bool();
            let event_types = $typ::EVENT_TYPES.value().value_list();
            map.insert($slug, (slug, filterby, filterby_is_in_alert_type, event_types));
        }};
    }

    add_config!(DaemonConfig,        "daemon");
    add_config!(DHCPConfig,          "dhcp");
    add_config!(DNSConfig,           "dns");
    add_config!(FilterLogConfig,     "filterlog");
    add_config!(RouterConfig,        "router");
    add_config!(RouterBoardConfig,   "routerboard");
    add_config!(SnortConfig,         "snort");
    add_config!(SquidConfig,         "squid");
    add_config!(SwitchConfig,        "switch");
    add_config!(UserAuditConfig,     "useraudit");
    add_config!(UserNoticeConfig,    "usernotice");
    add_config!(UserWarningConfig,   "userwarning");
    add_config!(VMwareConfig,        "vmware");
    add_config!(VPNServerConfig,     "vpnserver");
    add_config!(WindowsServerConfig, "windowsserver");

    map
});

static _PARSERS: Lazy<HashMap<&'static str, fn(&str, &[String], &HashMap<String, String>, &[String], bool, &str) -> (Option<String>, Option<Vec<String>>)>> = Lazy::new(|| {
    let mut map = HashMap::new();
    map.insert("daemon",        _parse_daemon        as _);
    map.insert("dhcp",          _parse_dhcp          as _);
    map.insert("dns",           _parse_dns           as _);
    map.insert("filterlog",     _parse_filterlog     as _);
    map.insert("router",        _parse_router        as _);
    map.insert("routerboard",   _parse_routerboard   as _);
    map.insert("snort",         _parse_snort         as _);
    map.insert("squid",         _parse_squid         as _);
    map.insert("switch",        _parse_switch        as _);
    map.insert("useraudit",     _parse_useraudit     as _);
    map.insert("usernotice",    _parse_usernotice    as _);
    map.insert("userwarning",   _parse_userwarning   as _);
    map.insert("vmware",        _parse_vmware        as _);
    map.insert("vpnserver",     _parse_vpnserver     as _);
    map.insert("windowsserver", _parse_windowsserver as _);
    map
});

#[inline(always)]
pub fn parse_ln(
    ln: &str,
    cls: ConfigType,
    object_list_of_names_and_addresses: &[String],
    object_dict_of_addresses_and_names: &HashMap<String, String>,
) -> (Option<String>, Option<Vec<String>>) {
    // __HAS_TEST__

    if _is_invalid_ln(ln) {
        return (None, None);
    }

    let (slug, filterby, filterby_is_in_alert_type, event_types) =
        match _PRECOMPUTED_VALUES.get(cls.get_slug()) {
            Some(v) => v,
            None => return (None, None),
        };

    if !filterby.is_empty() && !*filterby_is_in_alert_type {
        let filterby_bytes = filterby.as_bytes();
        if !ln.as_bytes().windows(filterby_bytes.len()).any(|window| window == filterby_bytes) {
            return (None, None);
        }
    }

    // get which function to run
    let func = match _PARSERS.get(slug.as_str()) {
        Some(f) => f,
        None => return (None, None),
    };

    func(
        ln,
        object_list_of_names_and_addresses,
        object_dict_of_addresses_and_names,
        event_types,
        *filterby_is_in_alert_type,
        filterby,
    )
}
