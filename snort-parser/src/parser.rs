use anyhow::{Result, anyhow};
use chrono::{NaiveDate, NaiveTime};
use log::info;
use regex::Regex;
use std::collections::HashMap;
use std::fs::{File, read_dir};
use std::io::{<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>eader};
use std::path::{Path, PathBuf};
use rayon::prelude::*;

use crate::config::Config;
use crate::types::{SnortRecord, Sensor, SnortAggregations};
use crate::database::DatabaseManager;

pub struct SnortLogParser {
    config: Config,
    snort_pattern: Regex,
    sensors: Vec<Sensor>,
}

impl SnortLogParser {
    pub fn new(config: Config) -> Result<Self> {
        // Snort log pattern based on the Python implementation
        // Pattern: 2000-01-01 00:00:00 Sensor-1 (auth/alert) [snort] [1:1448:20] POLICY-OTHER Microsoft [Classification: Generic Command] [Priority: 3] {TCP} 1.2.3.4:94 -> 5.6.7.8:32
        let snort_pattern = Regex::new(
            r"^(\d{4}-\d{2}-\d{2})\s+(\d{2}:\d{2}:\d{2})\s+(\S+)\s+\([^)]+\)\s+\[snort\]\s+\[(\d+:\d+:\d+)\]\s+(.+?)\s+\[Classification: (.+?)\]\s+\[Priority: (\d+)\]\s+\{(\w+)\}\s+([\d\.]+)(?::(\d+))?\s+->\s+([\d\.]+)(?::(\d+))?"
        )?;
        
        // For now, we'll use a simple sensor list. In a real implementation,
        // you'd load this from the database or configuration
        let sensors = vec![
            Sensor {
                name: "Sensor-1".to_string(),
                address: "***********".to_string(),
            },
            // Add more sensors as needed
        ];
        
        Ok(Self {
            config,
            snort_pattern,
            sensors,
        })
    }
    
    /// Process all log files in the configured directory
    pub fn process_logs(&self, db_manager: &DatabaseManager) -> Result<()> {
        let log_files = self.find_log_files()?;
        
        info!("Found {} log files to process", log_files.len());
        
        for log_file in log_files {
            self.process_log_file(&log_file, db_manager)?;
        }
        
        Ok(())
    }
    
    /// Find all .log files in the logs directory
    fn find_log_files(&self) -> Result<Vec<PathBuf>> {
        let mut log_files = Vec::new();
        
        if !self.config.logs_dir.exists() {
            return Err(anyhow!("Logs directory does not exist: {:?}", self.config.logs_dir));
        }
        
        for entry in read_dir(&self.config.logs_dir)? {
            let entry = entry?;
            let path = entry.path();
            
            if path.is_file() && path.extension().map_or(false, |ext| ext == "log") {
                log_files.push(path);
            }
        }
        
        log_files.sort();
        Ok(log_files)
    }
    
    /// Process a single log file
    fn process_log_file(&self, log_file: &Path, db_manager: &DatabaseManager) -> Result<()> {
        info!("Processing log file: {:?}", log_file);

        let log_date = self.extract_date_from_filename(log_file)?;

        // Check if already processed (unless force is enabled)
        if !self.config.force && self.is_already_processed(&log_date)? {
            info!("Log for {} already processed, skipping", log_date);
            return Ok(());
        }

        // For large files, use streaming approach to avoid memory issues
        let file_size = std::fs::metadata(log_file)?.len();
        const LARGE_FILE_THRESHOLD: u64 = 100 * 1024 * 1024; // 100MB

        let mut sensor_records: HashMap<String, Vec<SnortRecord>> = HashMap::new();
        let mut parsed_count = 0;
        let mut total_lines = 0;

        if file_size > LARGE_FILE_THRESHOLD {
            // Stream processing for large files
            info!("Large file detected ({}MB), using streaming approach", file_size / 1024 / 1024);

            let file = File::open(log_file)?;
            let reader = BufReader::new(file);

            for line in reader.lines() {
                total_lines += 1;
                let line = line?;

                if let Some(record) = self.parse_line(&line)? {
                    let sensor_name = self.determine_sensor(&record)?;
                    sensor_records.entry(sensor_name).or_default().push(record);
                    parsed_count += 1;
                }

                // Process in batches to avoid memory buildup
                if total_lines % 1_000_000 == 0 {
                    info!("Processed {} lines so far...", total_lines);
                }
            }
        } else {
            // Parallel processing for smaller files
            let file = File::open(log_file)?;
            let reader = BufReader::new(file);
            let lines: Result<Vec<String>, _> = reader.lines().collect();
            let lines = lines?;

            total_lines = lines.len();
            info!("Read {} lines from {:?}, processing in parallel", total_lines, log_file);

            // Parse lines in parallel
            let parsed_records: Vec<Option<SnortRecord>> = lines
                .par_iter()
                .map(|line| self.parse_line(line).unwrap_or(None))
                .collect();

            // Group by sensor
            for record_opt in parsed_records {
                if let Some(record) = record_opt {
                    let sensor_name = self.determine_sensor(&record)?;
                    sensor_records.entry(sensor_name).or_default().push(record);
                    parsed_count += 1;
                }
            }
        }

        info!("Parsed {} out of {} lines from {:?}", parsed_count, total_lines, log_file);
        
        // Process each sensor's records
        for (sensor_name, records) in sensor_records {
            if records.is_empty() {
                continue;
            }
            
            info!("Processing {} records for sensor {}", records.len(), sensor_name);
            
            // Create database and tables
            let database_name = self.config.get_database_name(&log_date, &sensor_name);
            db_manager.create_database(&database_name)?;
            db_manager.create_main_table(&database_name)?;
            
            // Insert main records
            db_manager.insert_records(&database_name, &records)?;
            
            // Generate and insert aggregations
            let aggregations = self.generate_aggregations(&records);
            db_manager.insert_aggregations(&database_name, &aggregations)?;

            // Insert visitor analysis
            db_manager.insert_visitor_analysis(&database_name, &records)?;

            // Mark as processed
            self.mark_as_processed(&log_date, &sensor_name)?;
        }
        
        Ok(())
    }
    
    /// Parse a single log line
    fn parse_line(&self, line: &str) -> Result<Option<SnortRecord>> {
        // Skip invalid lines
        if self.is_invalid_line(line) {
            return Ok(None);
        }
        
        // Check if line contains Snort filter
        if !line.contains("[snort]") {
            return Ok(None);
        }
        
        // Apply regex pattern
        if let Some(captures) = self.snort_pattern.captures(line) {
            let date = NaiveDate::parse_from_str(&captures[1], "%Y-%m-%d")?;
            let time = NaiveTime::parse_from_str(&captures[2], "%H:%M:%S")?;
            
            let record = SnortRecord {
                date,
                time,
                gid_sid: captures[4].to_string(),
                description: captures[5].to_string(),
                classification: captures[6].to_string(),
                priority: captures[7].parse()?,
                protocol: captures[8].to_string(),
                source_ip: captures[9].to_string(),
                source_port: captures.get(10).and_then(|m| m.as_str().parse().ok()),
                destination_ip: captures[11].to_string(),
                destination_port: captures.get(12).and_then(|m| m.as_str().parse().ok()),
            };
            
            Ok(Some(record))
        } else {
            Ok(None)
        }
    }
    
    /// Check if a line should be skipped
    fn is_invalid_line(&self, line: &str) -> bool {
        line.is_empty() ||
        line.contains("ERROR name exceeds safe print buffer length") ||
        line.contains("ERROR length byte") ||
        line.contains("leads outside message") ||
        line.contains("Exiting on signal") ||
        line.contains("Now monitoring attacks") ||
        line.contains("spp_arpspoof") ||
        line.contains("because it is a directory, not a file") ||
        !line.chars().next().map_or(false, |c| c.is_ascii_digit())
    }
    
    /// Extract date from log filename
    fn extract_date_from_filename(&self, path: &Path) -> Result<String> {
        // Assuming filename format contains date like: snort_2023-05-12.log
        let filename = path.file_stem()
            .and_then(|s| s.to_str())
            .ok_or_else(|| anyhow!("Invalid filename: {:?}", path))?;
        
        // Simple date extraction - you may need to adjust this based on your filename format
        let date_regex = Regex::new(r"(\d{4}-\d{2}-\d{2})")?;
        if let Some(captures) = date_regex.find(filename) {
            Ok(captures.as_str().to_string())
        } else {
            Err(anyhow!("Could not extract date from filename: {}", filename))
        }
    }
    
    /// Determine which sensor a record belongs to
    fn determine_sensor(&self, _record: &SnortRecord) -> Result<String> {
        // For simplicity, we'll use the first sensor
        // In a real implementation, you'd match based on the sensor field in the log
        Ok(self.sensors[0].name.clone())
    }
    
    /// Check if logs for a date have already been processed
    fn is_already_processed(&self, _date: &str) -> Result<bool> {
        // For simplicity, always return false
        // In a real implementation, you'd check for accomplished files
        Ok(false)
    }
    
    /// Mark logs as processed
    fn mark_as_processed(&self, _date: &str, _sensor: &str) -> Result<()> {
        // For simplicity, do nothing
        // In a real implementation, you'd create accomplished files
        Ok(())
    }
    
    /// Generate aggregations from records
    fn generate_aggregations(&self, records: &[SnortRecord]) -> SnortAggregations {
        let mut aggregations = SnortAggregations::new();
        
        for record in records {
            aggregations.add_record(record);
        }
        
        aggregations
    }
}
